<p align="center">
  <a href="https://github.com/lucide-icons/lucide">
    <img src="https://lucide.dev/package-logos/lucide-react-native.svg" alt="Lucide icon library for React Native applications." width="540">
  </a>
</p>

<p align="center">
Lucide icon library for React Native applications.
</p>

<div align="center">

  [![npm](https://img.shields.io/npm/v/lucide-react-native?color=blue)](https://www.npmjs.com/package/lucide-react-native)
  ![NPM Downloads](https://img.shields.io/npm/dw/lucide-react-native)
  [![GitHub](https://img.shields.io/github/license/lucide-icons/lucide)](https://lucide.dev/license)
</div>

<p align="center">
  <a href="https://lucide.dev/guide/">About</a>
  ·
  <a href="https://lucide.dev/icons/">Icons</a>
  ·
  <a href="https://lucide.dev/guide/packages/lucide-react-native">Documentation</a>
  ·
  <a href="https://lucide.dev/license">License</a>
</p>

# Lucide React Native

Implementation of the lucide icon library for React Native applications.

## Installation

```sh
pnpm add lucide-react-native
```

```sh
npm install lucide-react-native
```

```sh
yarn add lucide-react-native
```

```sh
bun add lucide-react-native
```

## Documentation

For full documentation, visit [lucide.dev](https://lucide.dev/guide/packages/lucide-react-native)

## Community

Join the [Discord server](https://discord.gg/EH6nSts) to chat with the maintainers and other users.

## License

Lucide is licensed under the ISC license. See [LICENSE](https://lucide.dev/license).

## Sponsors

<a href="https://vercel.com?utm_source=lucide&utm_campaign=oss">
  <img src="https://lucide.dev/vercel.svg" alt="Powered by Vercel" width="200" />
</a>

<a href="https://www.digitalocean.com/?refcode=b0877a2caebd&utm_campaign=Referral_Invite&utm_medium=Referral_Program&utm_source=badge"><img src="https://lucide.dev/digitalocean.svg" width="200" alt="DigitalOcean Referral Badge" /></a>

### Awesome backers 🍺

<a href="https://www.scipress.io?utm_source=lucide"><img src="https://lucide.dev/sponsors/scipress.svg" width="180" alt="Scipress sponsor badge" /></a>
<a href="https://github.com/pdfme/pdfme"><img src="https://lucide.dev/sponsors/pdfme.svg" width="180" alt="pdfme sponsor badge" /></a>
