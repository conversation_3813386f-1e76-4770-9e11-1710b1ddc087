import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTransactions } from '@/contexts/TransactionContext';
import TransactionCard from '@/components/TransactionCard';
import { Colors } from '@/constants/colors';
import { Typography } from '@/constants/typography';
import { Search, Filter, X } from 'lucide-react-native';
import V1DigitalFooter from '@/components/V1DigitalFooter';

export default function TransactionsScreen() {
  const { 
    state, 
    getFilteredTransactions, 
    setFilters, 
    clearFilters 
  } = useTransactions();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'ALL' | 'SALE' | 'PAYMENT'>('ALL');
  const [paymentFilter, setPaymentFilter] = useState<'BOTH' | 'CASH' | 'UPI'>('BOTH');
  const [refreshing, setRefreshing] = useState(false);

  const filteredTransactions = getFilteredTransactions();

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const applyFilters = () => {
    setFilters({
      type: filterType,
      paymentMethod: paymentFilter,
      searchTerm: searchTerm.trim() || undefined,
    });
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setFilterType('ALL');
    setPaymentFilter('BOTH');
    clearFilters();
  };

  React.useEffect(() => {
    applyFilters();
  }, [searchTerm, filterType, paymentFilter]);

  const formatAmount = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const getTotalAmount = () => {
    return filteredTransactions.reduce((sum, transaction) => {
      return sum + (transaction.type === 'SALE' ? transaction.totalAmount : -transaction.totalAmount);
    }, 0);
  };

  const getFilterButtonStyle = (isActive: boolean) => [
    styles.filterButton,
    isActive && styles.filterButtonActive
  ];

  const getFilterTextStyle = (isActive: boolean) => [
    styles.filterButtonText,
    isActive && styles.filterButtonTextActive
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Transactions</Text>
        <Text style={styles.subtitle}>
          {filteredTransactions.length} transactions • {formatAmount(getTotalAmount())}
        </Text>
      </View>

      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Search size={20} color={Colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            value={searchTerm}
            onChangeText={setSearchTerm}
            placeholder="Search by amount or description..."
            placeholderTextColor={Colors.textTertiary}
          />
          {searchTerm.length > 0 && (
            <TouchableOpacity onPress={() => setSearchTerm('')}>
              <X size={20} color={Colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View style={styles.filtersSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filtersContent}>
          {/* Transaction Type Filters */}
          <TouchableOpacity
            style={getFilterButtonStyle(filterType === 'ALL')}
            onPress={() => setFilterType('ALL')}
          >
            <Text style={getFilterTextStyle(filterType === 'ALL')}>All</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={getFilterButtonStyle(filterType === 'SALE')}
            onPress={() => setFilterType('SALE')}
          >
            <Text style={getFilterTextStyle(filterType === 'SALE')}>Sales</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={getFilterButtonStyle(filterType === 'PAYMENT')}
            onPress={() => setFilterType('PAYMENT')}
          >
            <Text style={getFilterTextStyle(filterType === 'PAYMENT')}>Payments</Text>
          </TouchableOpacity>

          {/* Payment Method Filters */}
          <View style={styles.filterSeparator} />
          <TouchableOpacity
            style={getFilterButtonStyle(paymentFilter === 'BOTH')}
            onPress={() => setPaymentFilter('BOTH')}
          >
            <Text style={getFilterTextStyle(paymentFilter === 'BOTH')}>All Methods</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={getFilterButtonStyle(paymentFilter === 'CASH')}
            onPress={() => setPaymentFilter('CASH')}
          >
            <Text style={getFilterTextStyle(paymentFilter === 'CASH')}>Cash</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={getFilterButtonStyle(paymentFilter === 'UPI')}
            onPress={() => setPaymentFilter('UPI')}
          >
            <Text style={getFilterTextStyle(paymentFilter === 'UPI')}>UPI</Text>
          </TouchableOpacity>

          {/* Clear Filters */}
          {(filterType !== 'ALL' || paymentFilter !== 'BOTH' || searchTerm.length > 0) && (
            <>
              <View style={styles.filterSeparator} />
              <TouchableOpacity style={styles.clearButton} onPress={clearAllFilters}>
                <X size={16} color={Colors.error} />
                <Text style={styles.clearButtonText}>Clear</Text>
              </TouchableOpacity>
            </>
          )}
        </ScrollView>
      </View>

      <ScrollView
        style={styles.transactionsList}
        contentContainerStyle={styles.transactionsContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {state.loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading transactions...</Text>
          </View>
        ) : filteredTransactions.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No transactions found</Text>
            <Text style={styles.emptySubtext}>
              {state.transactions.length === 0 
                ? 'Add your first transaction to get started'
                : 'Try adjusting your filters'
              }
            </Text>
          </View>
        ) : (
          filteredTransactions.map((transaction) => (
            <TransactionCard
              key={transaction.id}
              transaction={transaction}
            />
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: 20,
    paddingBottom: 12,
  },
  title: {
    fontSize: Typography.headerLarge,
    fontWeight: Typography.bold,
    color: Colors.textPrimary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: Typography.caption,
    color: Colors.textSecondary,
  },
  searchSection: {
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.cardBackground,
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: Typography.body,
    color: Colors.textPrimary,
  },
  filtersSection: {
    marginBottom: 12,
  },
  filtersContent: {
    paddingHorizontal: 20,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.cardBackground,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  filterButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  filterButtonText: {
    fontSize: Typography.caption,
    fontWeight: Typography.medium,
    color: Colors.textSecondary,
  },
  filterButtonTextActive: {
    color: Colors.cardBackground,
  },
  filterSeparator: {
    width: 1,
    height: 24,
    backgroundColor: Colors.separator,
    alignSelf: 'center',
    marginHorizontal: 4,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.cardBackground,
    borderWidth: 1,
    borderColor: Colors.error,
    gap: 4,
  },
  clearButtonText: {
    fontSize: Typography.caption,
    fontWeight: Typography.medium,
    color: Colors.error,
  },
  transactionsList: {
    flex: 1,
  },
  transactionsContent: {
    paddingBottom: 100,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: Typography.body,
    color: Colors.textSecondary,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: Typography.body,
    fontWeight: Typography.medium,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: Typography.caption,
    color: Colors.textTertiary,
    textAlign: 'center',
  },
});